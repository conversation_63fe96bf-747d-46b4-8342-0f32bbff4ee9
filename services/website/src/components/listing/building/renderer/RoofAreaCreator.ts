import {computed, ComputedRef, effectScope, Ref, ref, watch} from "vue";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {Floor, RoofArea, Shape, ShapeRepresentation, Vector3D} from "@/adapter/graphql/generated/graphql";
import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";
import {Matrix4, Plane, Vector3} from "three";
import {WallRoofPoint} from "@/components/listing/building/renderer/BuildingComponent";
import {v4 as uuidv4} from "uuid";
import {BUILDING_EPSILON, createEmptyShapeRepresentation} from "@/components/listing/building/building";
import {matrix4ToTransformationMatrixArray, transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {Optional} from "@/model/Optional";
import {tCalculateBestFittingPlaneNormal, tCenterOfVectors3, tIsPolygon3DOnPlane, tIsPolygon3DRectangular, tIsPolygonClockwise3D, tPerimeterOfPolygon3D, tPolygon3DToBufferGeometry_000, tSurfaceAreaOfBufferGeometry} from "@/adapter/three/three-utility";


export class RoofAreaCreator {
    public static readonly TEMP_ROOF_AREA_ID = "__BUILDING__ROOF_AREA_CREATOR__TEMP_ROOF_AREA_ID__"
    public readonly tempRoofArea: Readonly<Ref<RoofArea>>
    public isWallRoofPointSelectionValid!: ComputedRef<boolean>
    public validRoomIds!: ComputedRef<ReadonlySet<string>>
    private floor!: ComputedRef<Optional<Floor>>
    private roomId!: ComputedRef<Optional<string>>
    private worldVertices!: ComputedRef<readonly Vector3[]>
    private selectedWallRoofPoints!: ComputedRef<readonly WallRoofPoint[]>
    private readonly reactivityScope = effectScope(true)
    private readonly renderer: WeakRef<BuildingRenderer>

    constructor(renderer: BuildingRenderer) {
        this.renderer = new WeakRef(renderer)

        this.reactivityScope.run(() => {
            this.selectedWallRoofPoints = computed<readonly WallRoofPoint[]>(() => renderer.selectionRaycaster.selection.value.filter(c => c.type === "WALL_ROOF_POINT") as WallRoofPoint[])
            this.worldVertices = computed<Vector3[]>(() => this.selectedWallRoofPoints.value.map(p => p.worldPosition))
            this.isWallRoofPointSelectionValid = computed<boolean>(() => this.worldVertices.value.length >= 3)
            this.floor = computed<Optional<Floor>>(() => this.isWallRoofPointSelectionValid.value
                ? this.renderer.deref()?.traversableBuilding.floorOf(this.selectedWallRoofPoints.value[0].wall) ?? null
                : null
            )
            this.validRoomIds = computed<ReadonlySet<string>>(() => {
                const roomIds: readonly Set<string>[] = this.selectedWallRoofPoints.value
                    .map(wrp => wrp.wall.roomIds ?? [])
                    .filter(roomIds => roomIds.length > 0)
                    .map(roomIds => new Set<string>(roomIds))
                const uniqueRoomIds = new Set<string>(roomIds.flatMap(ids => Array.from(ids)))

                const validRoomIds = new Set<string>()
                for (const uniqueRoomId of uniqueRoomIds) {
                    if (roomIds.every(ids => ids.has(uniqueRoomId))) {
                        validRoomIds.add(uniqueRoomId)
                    }
                }
                return validRoomIds
            })
            this.roomId = computed<Optional<string>>(() => this.validRoomIds.value.size >= 1 ? this.validRoomIds.value.values().next().value! : null)

            watch(this.worldVertices, () => {
                this.updateRoofAreaData(this.tempRoofArea.value, true)
            })
        })

        this.tempRoofArea = ref<RoofArea>(this.createRoofArea(true)) //muss ref sein, damit vue reaktivität funktioniert
    }

    destroy() {
        this.reactivityScope.stop()
    }

    public async addRoofArea() {
        const renderer = this.renderer.deref()
        if (renderer === undefined) {
            console.warn("Renderer not found")
            return
        }
        const floor = this.floor.value
        if (floor === null) {
            console.warn("Floor not found")
            return
        }

        const newRoofArea = this.createRoofArea(false)

        if (floor.roofAreas === null || floor.roofAreas === undefined) {
            floor.roofAreas = [newRoofArea]
        } else {
            floor.roofAreas.push(newRoofArea)
        }

        renderer.traversableBuilding.addComponent(newRoofArea)

        await BuildingPipelineBuilder
            .create("PostRoofAreaCreation", renderer)
            .renewDisplayIdsAndRoomNumbers()
            .removeInvalidRelations()
            .save()
            .build()
            .execute()
            .then()

        this.renderer.deref()?.selectionRaycaster.reset()
    }

    // noinspection FunctionTooLongJS
    private updateRoofAreaData(roofArea: RoofArea, isTemp: boolean) {
        const [shapeRepresentation, localVertices, normal] = this.createShapeRepresentation(isTemp)

        let isFlat: boolean
        let area: number
        let perimeter: number
        let isRectangular: boolean
        let width: number
        let height: number

        if (localVertices.length >= 3) {
            const plane = new Plane();
            plane.setFromNormalAndCoplanarPoint(normal, localVertices[0]);
            isFlat = tIsPolygon3DOnPlane(localVertices, plane, BUILDING_EPSILON);
            // noinspection LocalVariableNamingConventionJS
            const geometry_000 = tPolygon3DToBufferGeometry_000(localVertices)
            area = tSurfaceAreaOfBufferGeometry(geometry_000);
            geometry_000.dispose()

            perimeter = tPerimeterOfPolygon3D(localVertices, BUILDING_EPSILON)
            const parameters = tIsPolygon3DRectangular(localVertices, BUILDING_EPSILON)
            isRectangular = parameters[0]
            width = parameters[1]
            height = parameters[2]

            if (isRectangular) {
                area = width * height
            } else {
                const sqrt = Math.sqrt(area)
                width = sqrt
                height = sqrt
            }
        } else {
            isFlat = false
            area = 0
            perimeter = 0
            isRectangular = false
            width = 0
            height = 0
        }

        roofArea.shapeRepresentation = shapeRepresentation
        roofArea.area = area
        roofArea.perimeter = perimeter
        roofArea.isFlat = isFlat
        roofArea.isTriangular = localVertices.length === 3
        roofArea.isRectangular = isRectangular
        roofArea.width = width
        roofArea.height = height
    }

    // noinspection FunctionTooLongJS
    private createRoofArea(isTemp: boolean): EnsureDefined<RoofArea> {
        const roomId = this.roomId.value

        if (!isTemp && roomId === null) {
            throw new Error("RoomId is null")
        }

        const roofArea: EnsureDefined<RoofArea> = {
            __typename: "RoofArea",
            id: isTemp ? RoofAreaCreator.TEMP_ROOF_AREA_ID : uuidv4(),
            displayId: null,
            customData: [],
            shapeRepresentation: createEmptyShapeRepresentation(),
            area: 0,
            perimeter: 0,
            isFlat: false,
            isTriangular: false,
            isRectangular: false,
            roomId: roomId ?? "",
            width: 0,
            height: 0,
        }

        this.updateRoofAreaData(roofArea, isTemp)

        return roofArea
    }

    // noinspection FunctionTooLongJS
    private createShapeRepresentation(isTemp: boolean): [EnsureDefined<ShapeRepresentation>, readonly Vector3[], Vector3] {
        if (!this.isWallRoofPointSelectionValid.value) {
            return [createEmptyShapeRepresentation(), [], new Vector3()]
        }

        let localVertices: readonly Vector3[]
        let roofAreaTransformationMatrix: Matrix4

        const worldVertices = this.worldVertices.value
        const isClockwise = tIsPolygonClockwise3D(worldVertices)

        if (isTemp) {
            localVertices = worldVertices
            roofAreaTransformationMatrix = new Matrix4().identity()
        } else {
            const renderer = this.renderer.deref()
            if (renderer === undefined) {
                console.warn("Renderer not found")
                return [createEmptyShapeRepresentation(), [], new Vector3()]
            }
            const building = renderer.building.value
            const floor = this.floor.value
            if (floor === null) {
                console.warn("Floor not found")
                return [createEmptyShapeRepresentation(), [], new Vector3()]
            }
            const buildingTransformation = transformationMatrixOfShapeRepresentation(building.shapeRepresentation)
            const floorTransformation = transformationMatrixOfShapeRepresentation(floor.shapeRepresentation)
            const floorWorldTransformation = buildingTransformation.clone().multiply(floorTransformation)
            const inverseFloorWorldTransformation = floorWorldTransformation.clone().invert()

            const rawLocalVertices = worldVertices.map(v => v.clone().applyMatrix4(inverseFloorWorldTransformation))
            const localCenter = tCenterOfVectors3(rawLocalVertices)

            localVertices = rawLocalVertices.map(v => v.sub(localCenter))
            roofAreaTransformationMatrix = new Matrix4().makeTranslation(localCenter)
        }

        const normal = tCalculateBestFittingPlaneNormal(localVertices)
        if (!isClockwise) {
            normal.negate()
        }

        // noinspection LocalVariableNamingConventionJS
        const rotationTransformationMatrixDirectingToNormal = new Matrix4().lookAt(
            new Vector3(0, 0, 0),
            normal,
            new Vector3(0, 1, 0)
        )
        // noinspection LocalVariableNamingConventionJS
        const inverseRotationTransformationMatrixDirectingToNormal = rotationTransformationMatrixDirectingToNormal.clone().invert()

        const directedLocalVertices = localVertices.map(v => v.clone().applyMatrix4(inverseRotationTransformationMatrixDirectingToNormal))
        const directedRoofAreaTransformationMatrix = roofAreaTransformationMatrix.clone().multiply(rotationTransformationMatrixDirectingToNormal)

        if (isClockwise) {
            directedLocalVertices.reverse()
        }

        const shapeRepresentation: EnsureDefined<ShapeRepresentation> = {
            __typename: "ShapeRepresentation",
            transformationMatrix: matrix4ToTransformationMatrixArray(directedRoofAreaTransformationMatrix),
            shape: {
                __typename: "Polygon",
                extrusion: 0,
                extrusionDirection: {
                    __typename: "Vector3D",
                    x: 0,
                    y: 0,
                    z: 0
                } satisfies EnsureDefined<Vector3D>,
                holes: [],
                vertices: directedLocalVertices.map(v => ({
                    __typename: "Vector3D",
                    x: v.x,
                    y: v.y,
                    z: v.z,
                } satisfies EnsureDefined<Vector3D>)),
            } satisfies EnsureDefined<Shape>
        }

        return [shapeRepresentation, localVertices, normal]
    }
}